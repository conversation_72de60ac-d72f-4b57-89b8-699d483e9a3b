{% extends "base.jinja2" %}

{% block title %}Log in{% endblock %}
{% block content %}
<div id="content-div">

  {# TITLE SINGLE #}
  <div>
    <style>
        me {
          width: 100%;
          text-align: center;
          padding-bottom: 8px;
        }

        me div {
          color: var(--color-text-title);
          padding-bottom: 14px;
          font-family: 'Noto Serif', serif;
          font-size: 24px;
          font-weight: 400;
          font-stretch: semi-condensed;
          font-style: italic;
        }

        me hr {
          display: block;
          height: 1px;
          border: 0;
          border-top: 1px solid var(--color-hr-lines);
          margin: 0;
          padding: 0;
        }
    </style>
    <div>Log in</div>
    <hr />
  </div>

  <form data-signals="{_users_submit_button_disable:false, _form_invalid:true}" data-on-submit="$_users_submit_button_disable = true;@post('/login_validate', {contentType: 'form'})">
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
    <div data-on-input="let email = document.getElementById('loginemail'); let pass = document.getElementById('loginpass'); $_form_invalid = !(email?.checkValidity() && pass?.checkValidity())">
      {{ render_partial('partials/forms-input.jinja2', namealwayschange='loginemail', label='Email', type = 'email', pattern="^[^@]+@[^@]+\.[^@]+$", errormessage="Invalid email address") }}
      {{ render_partial('partials/forms-input-password.jinja2', namealwayschange='loginpass', label='Password') }}
    </div>

  {# <form data-signals="{_users_submit_button_disable:false, _form_invalid:true}" data-on-submit="$_users_submit_button_disable = true;@post('/login_validate', {contentType: 'form'})">
    <div data-on-input="let email = document.getElementById('loginemail'); let pass = document.getElementById('loginpass'); $_form_invalid = !(email?.checkValidity() && pass?.checkValidity())">
      {{ render_partial('partials/forms-input.jinja2', namealwayschange='loginemail', label='Email', type = 'email', pattern="^[^@]+@[^@]+\.[^@]+$", errormessage="Invalid email address") }}
      {{ render_partial('partials/forms-input-password.jinja2', namealwayschange='loginpass', label='Password') }}
    </div> #}

    {# Forgot password? #}
    <div>
      <style>
          me {
            margin-top: -28px;
            text-align: right;
            position: relative;
            z-index: 3;
          }

          me a {
            color: var(--color-text-dark);
            text-decoration: none;
            font-weight: 400;
            font-stretch: semi-condensed;
          }

          me a:hover {
            text-decoration: underline;
            cursor: pointer;
          }
      </style>
      <a href="/forgotpasswordemailform">Forgot password?</a>
    </div>

    {# SUBMIT BUTTON #}
    <button type="submit" data-attr-disabled="$_form_invalid || $_users_submit_button_disable">
      <style>
          me {
            height: 40px;
            margin-top: 58px;
            width: 100%;
            display: block;
            background-color: transparent;
            color: var(--color-text-dark);
            border: 1px solid var(--color-text-dark);
            cursor: pointer;
            border-width: 1px;
            border-radius: 6px;
            font-family: 'Noto Sans', sans-serif;
            font-weight: 500;
            font-size: 18px;
            font-stretch: semi-condensed;
            text-align: center;
            transition: background-color 0.3s ease, color 0.3s ease;
          }

          me:hover {
            background-color: var(--color-background-dark);
            color: var(--color-text-bright);
          }

          me:disabled {
            background-color: var(--color-disabled-background);
            color: var(--color-text-black);
            opacity: 0.6;
            cursor: not-allowed;
          }

          me .button-spinner {
            display: none;
            width: 30px;
            height: 30px;
          }
      </style>
      <span class="button-text" data-attr-style="$_users_submit_button_disable ? 'display: none' : 'display: inline'">Log In</span>
      <img class="button-spinner" data-attr-style="$_users_submit_button_disable ? 'display: inline; margin-top: 4px; margin-bottom: 0px;' : 'display: none'"
            src="{{ url_for('static', path='/images/tube-spinner.svg') }}"
            alt="spinning..." />
    </button>

  </form>

  <div id="errordiv"></div>

  <div>
    <style>
        me {
          margin-top: 99px;
          margin-bottom: 0px;
          text-align: center;
        }

        /* Reduce margin on mobile devices */
        @media (max-width: 768px) {
          me {
            margin-top: 4rem;
          }
        }

        me a {
          color: var(--color-text-dark);
          text-decoration: none;
          font-weight: 400;
          font-stretch: semi-condensed;
        }

        me a:hover {
          text-decoration: underline;
          cursor: pointer;
        }
    </style>
    Don't have an account yet?
    <a href="/signup_form">Sign up</a>
  </div>



</div>

{% endblock content %}
