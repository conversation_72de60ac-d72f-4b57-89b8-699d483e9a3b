<div class="mycoolinput" {% if custom_margin %}style="margin-top: {{ custom_margin }}; margin-bottom: {{ custom_margin }};"{% endif %}>
  <input {% if pattern and pattern != '' %}pattern="{{ pattern }}"{% endif %} placeholder=" " type="{{ type | default('text') }}" {% if value %}value="{{ value }}"{% endif %} name="{{ name | default(namealwayschange) }}" id="{{ namealwayschange }}" {% if required is not defined or required %} required{% endif %} />
  <label class="mycoolinputslabel">
    {{ label | safe }}
  </label>
  <span class="input-group__error">{{ errormessage }}</span>
</div>


{# Here are the different input types you can use in HTML:

<input type="button">
<input type="checkbox">
<input type="color">
<input type="date">
<input type="datetime-local">
<input type="email">
<input type="file">
<input type="hidden">
<input type="image">
<input type="month">
<input type="number">
<input type="password">
<input type="radio">
<input type="range">
<input type="reset">
<input type="search">
<input type="submit">
<input type="tel">
<input type="text">
<input type="time">
<input type="url">
<input type="week">

#}


{# The value attribute specifies the value of an <input> element.
The value attribute is used differently for different input types:
For "button", "reset", and "submit" - it defines the text on the button
For "text", "password", and "hidden" - it defines the initial (default) value of the input field
For "checkbox", "radio", "image" - it defines the value associated with the input (this is also the value that is sent on submit) #}