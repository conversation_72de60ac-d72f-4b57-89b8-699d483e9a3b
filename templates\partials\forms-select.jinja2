<div class="mycoolselect" {% if custom_margin %}style="margin-top: {{ custom_margin }}; margin-bottom: {{ custom_margin }};"{% endif %}{% if margin_top and not custom_margin %}style="margin-top: {{ margin_top }};"{% endif %}{% if margin_bottom and not custom_margin %}style="margin-bottom: {{ margin_bottom }};"{% endif %}{% if margin_top and margin_bottom and not custom_margin %}style="margin-top: {{ margin_top }}; margin-bottom: {{ margin_bottom }};"{% endif %}>
  <select class="custom-float-select" name="{{ namealwayschange }}" id="{{ namealwayschange }}" onchange="this.setAttribute('value', this.value);" {% if required is not defined or required %} required{% endif %} >
    <option value=""></option>
    {% for option in optionslist %}
      {% if option is mapping %}
        <option value="{{ option.value }}"{% if option.selected %} selected{% endif %}>{{ option.value }}</option>
      {% else %}
        <option value="{{ option }}">{{ option }}</option>
      {% endif %}
    {% endfor %}
  </select>
  <label class="mycoolselectslabel">
    {{ label | safe }}
  </label>

  <span class="input-group__error">{{ errormessage }}</span>
</div>

<script>
// Global function to update arrow based on CSS variable
window.updateSelectArrowImage = function() {
  const computedStyle = getComputedStyle(document.documentElement);
  const colorTextBright = computedStyle.getPropertyValue('--color-text-bright').trim();

  document.querySelectorAll('.custom-float-select').forEach(function(select) {
    if (colorTextBright === '#F0DAC6') {
      select.style.backgroundImage = 'url(/static/images/icons8-arrow-down-64-dark.png)';
    } else {
      select.style.backgroundImage = 'url(/static/images/icons8-arrow-down-64.png)';
    }
  });
};

document.addEventListener("DOMContentLoaded", function() {
  // Function to update arrow based on CSS variable
  function updateArrowImage() {
    window.updateSelectArrowImage();
  }

  // For all custom-float-select elements on the page
  document.querySelectorAll('.custom-float-select').forEach(function(select) {
    const label = select.nextElementSibling && select.nextElementSibling.classList.contains('mycoolselectslabel') ? select.nextElementSibling : null;
    function updateLabel() {
      if (select && label) {
        if (select.value && select.value !== "") {
          label.classList.add('float-label');
        } else {
          label.classList.remove('float-label');
        }
      }
    }
    // Set value attribute for CSS compatibility
    select.setAttribute('value', select.value);
    updateLabel();
    select.addEventListener('change', function() {
      select.setAttribute('value', select.value);
      updateLabel();
    });
    // Also update label on page load in case value is set by browser autofill or server
    updateLabel();
  });

  // Update arrow images on page load
  updateArrowImage();
});
</script>

