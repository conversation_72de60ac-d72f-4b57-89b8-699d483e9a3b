{% extends "base.jinja2" %}

{% block title %}Calc01{% endblock %}
{% block content %}
  <div id="content-div">
    <style>
        @media (min-width: 769px) {
            .content-div {
                max-width: 1200px !important;
                margin: 3.75rem auto 3rem auto !important;
            }

            .content-div > div {
                max-width: none !important;
                padding-left: clamp(0.5rem, 5%, 4rem) !important;
                padding-right: clamp(0.5rem, 5%, 4rem) !important;
            }
        }

        @media (max-width: 768px) {
            .content-div {
                max-width: 51.25rem !important;
            }
        }

        @media (max-width: 480px) {
            .container {
                width: 98% !important;
            }
        }
    </style>

      {# TITLE SINGLE #}
    <div>
      <style>
          me {
            width: 100%;
            text-align: center;
            padding-bottom: 48px;
          }

          me div {
            color: var(--color-text-title);
            padding-bottom: 14px;
            font-family: 'Noto Serif', serif;
            font-size: 24px;
            font-weight: 400;
            font-stretch: semi-condensed;
            font-style: italic;
          }

          me hr {
            display: block;
            height: 1px;
            border: 0;
            border-top: 1px solid var(--color-hr-lines);
            margin: 0;
            padding: 0;
          }
      </style>
      <div>-(5) Calculation Title</div>
      <hr />
    </div>

    <form data-signals="{_users_submit_button_disable:false, _form_invalid:false}" data-on-submit="$_users_submit_button_disable = true;@post('/calc01_submit', {contentType: 'form'})">

      <div data-on-input="let PE = document.getElementById('PE'); let sp_flow = document.getElementById('sp_flow'); let sp_BOD5 = document.getElementById('sp_BOD5'); let sp_COD = document.getElementById('sp_COD'); let sp_SS = document.getElementById('sp_SS'); let sp_TN = document.getElementById('sp_TN'); let sp_P = document.getElementById('sp_P'); let safety_factor = document.getElementById('safety_factor'); let svi = document.getElementById('svi'); let primary = document.getElementById('primary'); let primary_duration = document.getElementById('primary_duration'); let sludge_storage_duration = document.getElementById('sludge_storage_duration'); let wwtp_type = document.getElementById('wwtp_type'); let separate_deintrification_chamber = document.getElementById('separate_deintrification_chamber'); let qsv = document.getElementById('qsv'); $_form_invalid = !(PE?.checkValidity() && sp_flow?.checkValidity() && sp_BOD5?.checkValidity() && sp_COD?.checkValidity() && sp_SS?.checkValidity() && sp_TN?.checkValidity() && sp_P?.checkValidity() && safety_factor?.checkValidity() && svi?.checkValidity() && primary?.checkValidity() && primary_duration?.checkValidity() && sludge_storage_duration?.checkValidity() && wwtp_type?.checkValidity() && separate_deintrification_chamber?.checkValidity() && qsv?.checkValidity())" data-on-change="let PE = document.getElementById('PE'); let sp_flow = document.getElementById('sp_flow'); let sp_BOD5 = document.getElementById('sp_BOD5'); let sp_COD = document.getElementById('sp_COD'); let sp_SS = document.getElementById('sp_SS'); let sp_TN = document.getElementById('sp_TN'); let sp_P = document.getElementById('sp_P'); let safety_factor = document.getElementById('safety_factor'); let svi = document.getElementById('svi'); let primary = document.getElementById('primary'); let primary_duration = document.getElementById('primary_duration'); let sludge_storage_duration = document.getElementById('sludge_storage_duration'); let wwtp_type = document.getElementById('wwtp_type'); let separate_deintrification_chamber = document.getElementById('separate_deintrification_chamber'); let qsv = document.getElementById('qsv'); $_form_invalid = !(PE?.checkValidity() && sp_flow?.checkValidity() && sp_BOD5?.checkValidity() && sp_COD?.checkValidity() && sp_SS?.checkValidity() && sp_TN?.checkValidity() && sp_P?.checkValidity() && safety_factor?.checkValidity() && svi?.checkValidity() && primary?.checkValidity() && primary_duration?.checkValidity() && sludge_storage_duration?.checkValidity() && wwtp_type?.checkValidity() && separate_deintrification_chamber?.checkValidity() && qsv?.checkValidity())">

      <!-- Two-column layout wrapper -->
      <div class="calc-two-column-wrapper">
        <style>
            me {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 4rem;
              margin: 0 auto;
              max-width: 1200px;
            }

            @media (max-width: 768px) {
              me {
                display: block;
                gap: 0;
                max-width: none;
              }
            }
        </style>

        <!-- Left Column: Specific loading quantities -->
        <div class="calc-column-left">
          <div>
            <style>
                me {
                  color: var(--color-text-subtitle);
                  padding-bottom: 16px;
                  font-family: "Noto Sans", sans-serif;
                  font-size: 20px;
                  font-weight: 300;
                  font-stretch: normal;
                  font-style: italic;
                }
            </style>
            Specific loading quantities
          </div>

      {{ render_partial('partials/forms-select-i.jinja2',
          custom_margin='0.0rem',
          namealwayschange='select_customer',
          required=False,
          label='Select Customer',
          optionslist=customer_names,
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext='The info of the selected customer will be showed on the pdf report.'
            ))
      }}


      {{ render_partial('partials/forms-input-i.jinja2',
          custom_margin='0.0rem',
          namealwayschange='PE',
          label='Population Equivalent',
          type = 'text',
          pattern="1000|[5-9]|[1-9][0-9]|[1-9][0-9]{2}",
          errormessage="Enter an integer between 5 and 1000",
          value="100",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="(Equivalent) Population that loads the biological treatment system",
            infotitle="Population Equivalent",
            infounit="Equivalent Population",
            inforange="Integer, 5-1000"
            ))
      }}

      {{ render_partial('partials/forms-input-i.jinja2',
          custom_margin='0.0rem',
          namealwayschange='sp_flow',
          label='Qin per PE',
          type = 'text',
          pattern="^(0|[1-9]|[1-9][0-9]|1[0-9]{2}|2[0-9]{2}|300)$",
          errormessage="Enter an integer between 0 and 300",
          value="150",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Daily wastewater flow per population equivalent",
            infotitle="Qin per PE",
            infounit="L/PE/d",
            inforange="Integer, 0-300"
            ))
      }}

      {{ render_partial('partials/forms-input-i.jinja2',
          custom_margin='0.0rem',
          namealwayschange='sp_BOD5',
          label='BOD<sub>5</sub>',
          type = 'text',
          pattern="^((1[0-9]|[2-8][0-9]|90)(\.\d+)?|10|90)$",
          errormessage="Please enter a floating-point number between 10 and 90",
          value="60",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Biochemical oxygen demand per population equivalent per day",
            infotitle="BOD<sub>5</sub>",
            infounit="gr/PE/d",
            inforange="float, 10-90"
            ))
      }}

      {{ render_partial('partials/forms-input-i.jinja2',
          custom_margin='0.0rem',
          namealwayschange='sp_COD',
          label='COD',
          type = 'text',
          pattern="^(1[0-7][0-9](\.\d+)?|180(\.0+)?|[2-9][0-9](\.\d+)?|20(\.0+)?)$",
          errormessage="Enter a floating point number between 20 and 180.",
          value="120",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Chemical oxygen demand per population equivalent per day",
            infotitle="COD",
            infounit="gr/PE/d",
            inforange="float, 20-180"
            ))
      }}

      {{ render_partial('partials/forms-input-i.jinja2',
          custom_margin='0.0rem',
          namealwayschange='sp_SS',
          label='Suspended Solids',
          type = 'text',
          pattern="^(90(\.0+)?|[3-8][5-9](\.\d+)?|[4-8][0-9](\.\d+)?|35(\.0+)?|[3-8][6-9](\.\d+)?)$",
          errormessage="Enter a number between 35 and 90 (decimals allowed).",
          value="70",
          infohtml=render_partial("partials/forms-info-popover-content.jinja2",
            infotext="Suspended solids per population equivalent per day",
            infotitle="Suspended Solids",
            infounit="gr/PE/d",
            inforange="float, 35-90"
            ))
      }}

          {{ render_partial('partials/forms-input-i.jinja2',
              custom_margin='0.0rem',
              namealwayschange='sp_TN',
              label='Total Nitrogen',
              type = 'text',
              pattern="^(0|[1-9]\d*)(\.\d+)?$",
              errormessage="Enter a number greater than or equal to 0.",
              value="11",
              infohtml=render_partial("partials/forms-info-popover-content.jinja2",
                infotext="Total nitrogen per population equivalent per day",
                infotitle="Total Nitrogen",
                infounit="gr/PE/d",
                inforange="float, ≥0"
                ))
          }}

          {{ render_partial('partials/forms-input-i.jinja2',
              custom_margin='0.0rem',
              namealwayschange='sp_P',
              label='Phosphorus',
              type = 'text',
              pattern="^(0|[1-9]\d*)(\.\d+)?$",
              errormessage="Enter a number greater than or equal to 0.",
              value="1.8",
              infohtml=render_partial("partials/forms-info-popover-content.jinja2",
                infotext="Phosphorus per population equivalent per day",
                infotitle="Phosphorus",
                infounit="gr/PE/d",
                inforange="float, ≥0"
                ))
          }}
        </div>

        <!-- Right Column: Design Parameters -->
        <div class="calc-column-right">
          <div>
            <style>
                me {
                  color: var(--color-text-subtitle);
                  padding-bottom: 16px;
                  font-family: "Noto Sans", sans-serif;
                  font-size: 20px;
                  font-weight: 300;
                  font-stretch: normal;
                  font-style: italic;
                }
            </style>
            Design Parameters
          </div>

          {{ render_partial('partials/forms-input-i.jinja2',
              custom_margin='0.0rem',
              namealwayschange='safety_factor',
              label='Safety Factor',
              type = 'text',
              pattern="^([1-9]\d*)(\.\d+)?$",
              errormessage="Enter a number greater than or equal to 1.",
              value="1.8",
              infohtml=render_partial("partials/forms-info-popover-content.jinja2",
                infotext="Safety factor for design calculations",
                infotitle="Safety Factor",
                infounit="-",
                inforange="float, ≥1"
                ))
          }}

          {{ render_partial('partials/forms-input-i.jinja2',
              custom_margin='0.0rem',
              namealwayschange='svi',
              label='Sludge Volume Index',
              type = 'text',
              pattern="^-?\d+$",
              errormessage="Enter an integer.",
              value="100",
              infohtml=render_partial("partials/forms-info-popover-content.jinja2",
                infotext="Sludge settling index",
                infotitle="Sludge Volume Index",
                infounit="L/kg",
                inforange="Integer"
                ))
          }}


          {{ render_partial('partials/forms-select-i.jinja2',
              custom_margin='0.0rem',
              namealwayschange='primary',
              label='Is there Primary Treatment?',
              optionslist = [
                {'value': 'True', 'selected': False},
                {'value': 'False', 'selected': True}
              ],
              infohtml=render_partial("partials/forms-info-popover-content.jinja2",
                infotext="Presence or absence of primary treatment (related to tpr)",
                infotitle="Is there Primary Treatment?"
                ))
          }}

          {{ render_partial('partials/forms-select-i.jinja2',
              custom_margin='0.0rem',
              namealwayschange='primary_duration',
              label='Primary Duration',
              optionslist = [
                {'value': '0.0', 'selected': False},
                {'value': '0.5', 'selected': False},
                {'value': '1.0', 'selected': False},
                {'value': '1.5', 'selected': False},
                {'value': '2.0', 'selected': False},
              ],
              infohtml=render_partial("partials/forms-info-popover-content.jinja2",
                infotext="Duration of primary treatment<br><br>0 if there is no Primary Treatment.",
                infotitle="Primary Duration",
                infounit="Hours"
                ))
          }}

          {{ render_partial('partials/forms-input-i.jinja2',
              custom_margin='0.0rem',
              namealwayschange='sludge_storage_duration',
              label='Sludge Storage Duration',
              type='text',
              pattern="^(0|[1-9]|1[0-2])$",
              errormessage="Enter an integer between 0 and 12.",
              value="6",
              infohtml=render_partial("partials/forms-info-popover-content.jinja2",
                infotext="Sludge storage duration in the primary treatment tank",
                infotitle="Sludge Storage Duration",
                infounit="Months",
                inforange="Integer, 0-12"
                ))
          }}

          {{ render_partial('partials/forms-select-i.jinja2',
              custom_margin='0.0rem',
              namealwayschange='wwtp_type',
              label='Treatment Type',
              optionslist = [
                {'value': 'ORG. C', 'selected': False},
                {'value': 'NITR', 'selected': False},
                {'value': 'DENITR', 'selected': False},
                {'value': 'STAB', 'selected': True},
              ],
              infohtml=render_partial("partials/forms-info-popover-content.jinja2",
                infotext="Type of biological treatment process: 'BOD5 (organic) removal', 'Nitrification procedure', 'Denitrification procedure', 'Sludge Stabilization', 'Phosphorus Removal'",
                infotitle="Treatment Type"
                ))
          }}

          {{ render_partial('partials/forms-select-i.jinja2',
              custom_margin='0.0rem',
              namealwayschange='separate_deintrification_chamber',
              label='Is dentrification chamber separated from reactor',
              optionslist = [
                {'value': 'True', 'selected': False},
                {'value': 'False', 'selected': True}
              ],
              infohtml=render_partial("partials/forms-info-popover-content.jinja2",
                infotext="Whether denitrification occurs in a separate (isolated) chamber or within the reactor tank",
                infotitle="Is dentrification chamber separated from reactor"
                ))
          }}

          {{ render_partial('partials/forms-input-i.jinja2',
              custom_margin='0.0rem',
              namealwayschange='qsv',
              label='q<sub>sv</sub>',
              type='text',
              pattern="^(650(\.0+)?|([0-9]{1,2}|[1-5][0-9]{2}|6[0-4][0-9]|[1-9][0-9]?)(\.\d+)?|0(\.0+)?)$",
              errormessage="Enter a number less than or equal to 650.",
              value="650",
              infohtml=render_partial("partials/forms-info-popover-content.jinja2",
                infotext="Surface loading rate of sludge volume in the secondary settling tank. Mass settling rate cm/h",
                infotitle="q<sub>sv</sub>",
                infounit="l/(m<sup>2</sup>*h)",
                inforange="Float, ≤650"
                ))
          }}
        </div>
      </div>

      <!-- Submit button container -->
      <div class="calc-submit-container">
        <style>
            me {
              display: flex;
              justify-content: flex-end;
              margin-top: 1.5rem;
              grid-column: 2;
            }

            @media (max-width: 768px) {
              me {
                justify-content: stretch;
                margin-top: 1.75rem;
                grid-column: unset;
              }
            }
        </style>

        {# SUBMIT BUTTON #}
        <button type="submit" data-attr-disabled="$_form_invalid || $_users_submit_button_disable">
          <style>
              me {
                  height: 40px;
                  margin-top: 0;
                  margin-bottom: 42px;
                  width: auto;
                  min-width: 200px;
                  max-width: 300px;
                  display: block;
                  background-color: transparent;
                  color: var(--color-text-dark);
                  border: 1px solid var(--color-text-dark);
                  cursor: pointer;
                  border-width: 1px;
                  border-radius: 6px;
                  font-family: 'Noto Sans', sans-serif;
                  font-weight: 500;
                  font-size: 18px;
                  font-stretch: semi-condensed;
                  text-align: center;
                  transition: background-color 0.3s ease, color 0.3s ease;
                  padding: 0 2rem;
              }

              me:hover {
                  background-color: var(--color-background-dark);
                  color: var(--color-text-bright);
              }

              me:disabled {
                  background-color: var(--color-disabled-background);
                  color: var(--color-text-black);
                  opacity: 0.6;
                  cursor: not-allowed;
              }

              me .button-spinner {
                  display: none;
                  width: 30px;
                  height: 30px;
              }

              @media (max-width: 768px) {
                me {
                  width: 100% !important;
                  min-width: unset;
                  max-width: unset;
                  margin-top: 28px;
                }
              }
          </style>
          <span class="button-text" data-attr-style="$_users_submit_button_disable ? 'display: none' : 'display: inline'">Submit</span>
          <img class="button-spinner" data-attr-style="$_users_submit_button_disable ? 'display: inline; margin-top: 4px; margin-bottom: 0px;' : 'display: none'"
                src="{{ url_for('static', path='/images/tube-spinner.svg') }}"
                alt="spinning..." />
        </button>
      </div>
      </div>
    </form>

    <div id="errordiv"></div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
      const primarySelect = document.getElementById('primary');
      const primaryDurationSelect = document.getElementById('primary_duration');

      function updatePrimaryDuration() {
        if (primarySelect.value === 'False') {
          primaryDurationSelect.value = '0.0';
          primaryDurationSelect.disabled = true;
          primaryDurationSelect.setAttribute('value', '0.0');
          // Trigger the label update logic in the partial
          primaryDurationSelect.dispatchEvent(new Event('change', { bubbles: true }));
        } else {
          primaryDurationSelect.disabled = false;
          // Trigger the label update logic in the partial
          primaryDurationSelect.dispatchEvent(new Event('change', { bubbles: true }));
        }
      }

      // Initial state
      updatePrimaryDuration();

      // Listen for changes
      primarySelect.addEventListener('change', updatePrimaryDuration);
    });
    </script>

  </div>
{% endblock content %}